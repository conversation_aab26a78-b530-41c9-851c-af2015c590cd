/********** Template CSS **********/
:root {
    --primary: #eb232a;
    --secondary: #757575;
    --light: #F3F6F8;
    --dark: #0C2B4B;
}

.py-6 {
    padding-top: 6rem;
    padding-bottom: 6rem;
}

.my-6 {
    margin-top: 6rem;
    margin-bottom: 6rem;
}

.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: 99;
}


/*** Spinner ***/
#spinner {
    opacity: 0;
    visibility: hidden;
    transition: opacity .5s ease-out, visibility 0s linear .5s;
    z-index: 99999;
}

#spinner.show {
    transition: opacity .5s ease-out, visibility 0s linear 0s;
    visibility: visible;
    opacity: 1;
}


/*** Button ***/
.btn {
    font-weight: 500;
    transition: .5s;
}

.btn.btn-primary,
.btn.btn-outline-primary:hover {
    color: #FFFFFF;
}

.btn-square {
    width: 38px;
    height: 38px;
}

.btn-sm-square {
    width: 32px;
    height: 32px;
}

.btn-lg-square {
    width: 48px;
    height: 48px;
}

.btn-square,
.btn-sm-square,
.btn-lg-square {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: normal;
}


/*** Navbar ***/
.navbar.sticky-top {
    top: 0;
    transition: .5s;
    z-index: 1030;
}

.navbar .navbar-brand,
.navbar a.btn {
    height: 80px
}

.navbar .navbar-nav .nav-link {
    margin-right: 30px;
    padding: 25px 0;
    color: var(--dark);
    font-weight: 500;
    text-transform: uppercase;
    outline: none;
}

.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-link.active {
    color: var(--primary);
}

.navbar .dropdown-toggle::after {
    border: none;
    content: "\f107";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    vertical-align: middle;
    margin-left: 8px;
}

@media (max-width: 991.98px) {
    .navbar .navbar-nav .nav-link {
        margin-right: 0;
        padding: 10px 0;
    }

    .navbar .navbar-nav {
        border-top: 1px solid #EEEEEE;
    }
}

@media (min-width: 992px) {
    .navbar .nav-item .dropdown-menu {
        display: block;
        border: none;
        margin-top: 0;
        top: 150%;
        opacity: 0;
        visibility: hidden;
        transition: .5s;
    }

    .navbar .nav-item:hover .dropdown-menu {
        top: 100%;
        visibility: visible;
        transition: .5s;
        opacity: 1;
    }
}


/*** Header ***/
.carousel-caption {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    /* background: rgba(0, 0, 0, .75); */
    z-index: 1;
}

.carousel-control-prev,
.carousel-control-next {
    width: 15%;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary);
    border: 10px solid var(--primary);
}

@media (max-width: 768px) {
    #header-carousel .carousel-item {
        position: relative;
        min-height: 450px;
    }

    #header-carousel .carousel-item img {
        position: absolute;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}





.breadcrumb-item+.breadcrumb-item::before {
    color: #999999;
}


/*** Facts ***/
@media (min-width: 991.98px) {
    .facts {
        position: relative;
        margin-top: -75px;
        z-index: 1;
    }
}


/*** Courses ***/
.courses {
    min-height: 100vh;
    background: linear-gradient(rgba(255, 255, 255, .9), rgba(255, 255, 255, .9)), url() center center no-repeat;
    background-attachment: fixed;
    background-size: cover;
}

.courses-item .courses-overlay {
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, .5);
    overflow: hidden;
    opacity: 0;
    transition: .5s;
}

.courses-item:hover .courses-overlay {
    height: 100%;
    opacity: 1;
}


/*** Team ***/
.team-items {
    margin: -.75rem;
}

.team-item {
    padding: .75rem;
}

.team-item::after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    background: #FFFFFF;
    transition: .5s;
    z-index: -1;
}

.team-item:hover::after {
    height: 100%;
    background: var(--primary);
}

.team-item .team-social {
    position: absolute;
    width: 100%;
    height: 0;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, .75);
    overflow: hidden;
    opacity: 0;
    transition: .5s;
}

.team-item:hover .team-social {
    height: 100%;
    opacity: 1;
}


/*** Testimonial ***/
.testimonial-carousel .owl-dots {
    height: 40px;
    margin-top: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial-carousel .owl-dot {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    width: 20px;
    height: 20px;
    background: transparent;
    border: 2px solid var(--primary);
    transition: .5s;
}

.testimonial-carousel .owl-dot.active {
    width: 40px;
    height: 40px;
    background: var(--primary);
}

.testimonial-carousel .owl-item img {
    width: 150px;
    height: 150px;
}


/*** Footer ***/
.footer .btn.btn-link {
    display: block;
    margin-bottom: 5px;
    padding: 0;
    text-align: left;
    color: var(--light);
    font-weight: normal;
    text-transform: capitalize;
    transition: .3s;
}

.footer .btn.btn-link::before {
    position: relative;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: var(--light);
    margin-right: 10px;
}

.footer .btn.btn-link:hover {
    color: var(--primary);
    letter-spacing: 1px;
    box-shadow: none;
}

.copyright {
    background: #092139;
}

.copyright a {
    color: var(--primary);
}

.copyright a:hover {
    color: var(--light);
}


.main-heading {
    color: var(--dark);
    background-color: var(--light);
    padding: 15px 20px;
    border-left: 5px solid var(--primary);
    border-radius: 5px;
    margin-bottom: 25px;
    font-size: 1.8rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}


.salary-bar {
    margin-top: 150px;
    margin-left: 50px;
    height: 200px;
    display: flex;
    align-items: flex-end;
    gap: 20px;
}

.bar {
    width: 60px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: white;
    font-weight: bold;
    border-radius: 10px 10px 0 0;
}

.bar1 {
    height: 100px;
    background: #13d980;
}

.bar2 {
    height: 150px;
    background: #9a0a38;
}

.bar3 {
    height: 200px;
    background: #889d0f;
}

/* .logo-container {
  display: flex;
  align-items: center; 
  justify-content: center; 
  gap: 20px; 
}

.me-2 {
  width: 150px; 
  height: auto; 
} */

/* === Course Carousel Custom Styles === */

.courses-carousel .courses-item {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #eee;
  border-radius: 15px;
  overflow: hidden;
  height: 530px;
}

.courses-carousel .courses-item .text-center {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.courses-carousel .courses-item .text-center .btn {
    margin-top: auto;
}

.courses-carousel .courses-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.courses-carousel .courses-item .position-relative {
  overflow: hidden;
}

.courses-carousel .courses-item img {
  transition: transform 0.5s ease;
}

.courses-carousel .courses-item:hover img {
  transform: scale(1.05);
}

.courses-carousel .courses-item .badge {
  font-size: 0.8rem;
  font-weight: 600;
}

.courses-carousel .courses-item h5 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #d90429; /* Using a color from the existing theme */
}

.courses-carousel .courses-item p {
  font-size: 0.9rem;
  color: #555;
}

.courses-carousel .breadcrumb {
  font-size: 0.8rem;
}

.courses-carousel .btn {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom Owl Carousel Navigation */
.courses-carousel .owl-nav {
  position: absolute;
  top: -80px; /* Adjust as needed */
  right: 0;
  display: flex;
  gap: 10px;
}

.courses-carousel .owl-nav button.owl-prev,
.courses-carousel .owl-nav button.owl-next {
  width: 45px;
  height: 45px;
  background-color: #fff;
  border: 2px solid #d90429;
  color: #d90429;
  border-radius: 50%;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.courses-carousel .owl-nav button.owl-prev:hover,
.courses-carousel .owl-nav button.owl-next:hover {
  background-color: #d90429;
  color: #fff;
}

/* Custom Owl Carousel Dots */
.courses-carousel .owl-dots {
  margin-top: 30px;
  text-align: center;
}

.courses-carousel .owl-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #ddd;
  border-radius: 50%;
  margin: 0 5px;
  transition: background-color 0.3s ease;
}

.courses-carousel .owl-dot.active {
  background-color: #d90429;
}


