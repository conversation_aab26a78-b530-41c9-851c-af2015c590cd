# 🏗️ CADD Centre Kasaragod - Modern Professional Website

<div align="center">

![CADD Centre Logo](Public/CDC_Logo-03.svg)
![CADD Centre Logo](Public/CADD%20CENTRE%20LOGO%20SVG.svg)

[![Website](https://img.shields.io/badge/Website-Live-brightgreen)](https://ashiii2121.github.io/Cadd-centre/)
[![GitHub](https://img.shields.io/badge/GitHub-Repository-blue)](https://github.com/ashiii2121/Cadd-centre)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

**Premier Design Engineering Institute - Transforming Careers Through Technology**

[🌐 Visit Website](https://ashiii2121.github.io/Cadd-centre/) • [📞 Contact Us](tel:+************) • [💬 WhatsApp](https://wa.me/************)

</div>

---

## ✨ Overview

Welcome to the newly redesigned, modern website for **CADD Centre Kasaragod** - Premier Design Engineering Institute! This project features a complete visual overhaul with cutting-edge design principles, enhanced user experience, and professional aesthetics. Built to showcase our comprehensive range of AI-powered courses, expert faculty, and industry-leading training programs.

### 🎯 Key Improvements

- **🎨 Modern Design Language**: Clean, professional interface with contemporary styling
- **⚡ Enhanced User Experience**: Smooth animations, interactive elements, and intuitive navigation
- **📱 Mobile-First Approach**: Fully responsive design optimized for all devices
- **🚀 Performance Optimized**: Fast loading times and efficient code structure
- **🎯 Professional Branding**: Consistent visual identity throughout the website

## 📸 Website Preview

<div align="center">

### 🏠 Homepage Hero Section

![Homepage Hero](Public/bhim-banner01.png)

### 🎓 Course Showcase

![Courses](Public/Smart-Building-Design.png)

### 🏢 Modern Interface

![BIM Training](Public/BIM.png)

</div>

## 🌟 Modern Features

### 🎨 Design Excellence

- **Modern UI/UX**: Contemporary design with professional aesthetics
- **Custom CSS Variables**: Consistent theming with CSS custom properties
- **Advanced Typography**: Professional fonts (Inter & Poppins) for enhanced readability
- **Gradient Backgrounds**: Modern gradient effects and visual elements
- **Smooth Animations**: AOS (Animate On Scroll) library integration

### 🚀 Enhanced Functionality

- **Interactive Hero Carousel**: Dynamic slides with modern controls and indicators
- **Floating WhatsApp Button**: Direct communication channel with pulse animation
- **Modern Card Designs**: Hover effects and smooth transitions
- **Enhanced Navigation**: Sticky navbar with blur effects and active states
- **Professional Loading States**: Smooth loading animations and transitions

### 📱 Responsive Design

- **Mobile-First Approach**: Optimized for all screen sizes
- **Touch-Friendly Interface**: Enhanced mobile interactions
- **Cross-Browser Compatibility**: Works seamlessly across all modern browsers
- **Performance Optimized**: Fast loading times and efficient animations

### 🎯 User Experience

- **Smooth Scrolling**: Enhanced navigation between sections
- **Interactive Elements**: Engaging hover effects and micro-interactions
- **Professional Forms**: Modern form validation and styling
- **Accessibility Features**: WCAG compliant design elements

## 🎓 Our Courses

<div align="center">

| Course Category                                  | Duration |    Level     | Highlights                         |
| :----------------------------------------------- | :------: | :----------: | :--------------------------------- |
| **🤖 AI-Powered Smart Building Design**          |  40 hrs  |   Beginner   | Cutting-edge AI tools & techniques |
| **🏛️ Executive Diploma in Architectural Design** | 400 hrs  |   Beginner   | Comprehensive design concepts      |
| **🏗️ Executive Diploma in BIM**                  | 400 hrs  | Intermediate | Advanced BIM tools & techniques    |
| **🎨 Executive Diploma in Interior Design**      | 440 hrs  |   Beginner   | Creative space planning            |
| **⚙️ Executive Diploma in Structural Design**    | 200 hrs  | Intermediate | Engineering excellence             |
| **🔧 Master Certificate in MEP Design**          | 240 hrs  |   Advanced   | Building systems expertise         |
| **🏢 Master Certificate in Building Design**     | 160 hrs  | Intermediate | Modern construction techniques     |

</div>

## 🏆 Our Achievements

<div align="center">

### 📊 Impact Statistics

| Metric                  |           Achievement           |
| :---------------------- | :-----------------------------: |
| **👥 Students Trained** | 2.5M+ Engineers & Professionals |
| **🌍 Global Presence**  |      600+ Training Centers      |
| **🗺️ Countries**        |          30+ Worldwide          |
| **💼 Placement Rate**   |         100% Assistance         |
| **⭐ Industry Rating**  |     Asia's Biggest Network      |

</div>

## 🛠️ Technologies Used

<div align="center">

| Technology          | Version | Purpose                            |
| :------------------ | :-----: | :--------------------------------- |
| **HTML5**           | Latest  | Semantic structure & accessibility |
| **CSS3**            | Latest  | Modern styling & animations        |
| **JavaScript ES6+** | Latest  | Interactive functionality          |
| **Bootstrap 5**     |  5.3.0  | Responsive framework               |
| **Font Awesome**    |  6.4.0  | Modern icons                       |
| **AOS Library**     |  2.3.1  | Scroll animations                  |
| **Google Fonts**    | Latest  | Professional typography            |
| **jQuery**          |  3.4.1  | DOM manipulation                   |

</div>

## 🚀 Getting Started (Quick & Easy!)

As a static HTML website, getting started is incredibly simple – no complex server setup or build process is required!

1.  **Clone the Repository (if applicable):** If you obtained this project via a Git repository, clone it to your local machine:
    ```bash
    git clone <repository-url>
    ```
2.  **Open in Your Browser:** Navigate to the project directory and simply open any `.html` file (e.g., `index.html`, `about.html`, `courses.html`) directly in your preferred web browser. The website will load instantly!

## 📂 Project Structure

```
caddcenter/
├── 📄 *.html                 # Main website pages
├── 🎨 css/                   # Stylesheets & themes
├── ⚡ js/                    # JavaScript functionality
├── 📚 lib/                   # Third-party libraries
├── 🖼️ Public/               # Images & media assets
├── 🎯 scss/                  # SCSS source files
└── 📖 README.md              # Documentation
```

## 📞 Contact Information

<div align="center">

### 🏢 CADD Centre Kasaragod

**📍 Address:** III, Square 9 Mall, New Busstand, Kasaragod, Kerala, India

**📱 Phone:** [+91 9072844144](tel:+************)

**📧 Email:** [<EMAIL>](mailto:<EMAIL>)

**💬 WhatsApp:** [Chat with us](https://wa.me/************?text=Hi!%20I'm%20interested%20in%20CADD%20Centre%20courses.%20Please%20provide%20more%20information.)

---

### 🌐 Follow Us

[![Instagram](https://img.shields.io/badge/Instagram-E4405F?style=for-the-badge&logo=instagram&logoColor=white)](https://www.instagram.com/caddcentrekasaragod)
[![LinkedIn](https://img.shields.io/badge/LinkedIn-0077B5?style=for-the-badge&logo=linkedin&logoColor=white)](https://www.linkedin.com/company/cdcinternational-in)
[![YouTube](https://img.shields.io/badge/YouTube-FF0000?style=for-the-badge&logo=youtube&logoColor=white)](https://www.youtube.com/@caddcentrekasaragod840)
[![WhatsApp](https://img.shields.io/badge/WhatsApp-25D366?style=for-the-badge&logo=whatsapp&logoColor=white)](https://wa.me/************)

</div>

## 🚀 Quick Start

1. **Clone the repository**

   ```bash
   git clone https://github.com/ashiii2121/Cadd-centre.git
   cd Cadd-centre
   ```

2. **Open in browser**

   ```bash
   # Simply open index.html in your preferred browser
   open index.html  # macOS
   start index.html # Windows
   ```

3. **For development**
   ```bash
   # Use a local server for best experience
   npx serve .
   # or
   python -m http.server 8000
   ```

## ⚖️ License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

We welcome contributions! Please feel free to submit a Pull Request.

---

<div align="center">

**© 2024 CADD Centre Kasaragod. All Rights Reserved.**

_Designed with ❤️ by CDC Team_

**Transforming Careers Through Technology**

</div>
