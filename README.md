# 🚀 CADD Centre Training Institute Website

## ✨ Overview

Welcome to the official web presence for CADD Centre Training Institute! This project delivers a modern, responsive, and interactive website designed to showcase our diverse range of courses, highlight key features, introduce our dedicated team, share valuable testimonials, and provide essential contact information. Our mission is to offer a seamless and engaging user experience across all devices, making it easy for aspiring professionals to explore their future in design and engineering.

## 🌟 Features

*   **Comprehensive Course Catalog:** 📚 Explore detailed pages for various courses like AI Building Design, Executive Diplomas, Master Certificates, and more. Find your perfect learning path!
*   **Responsive & Adaptive Design:** 📱💻 Built with Bootstrap, our website ensures an optimal viewing and interaction experience on desktops, tablets, and mobile devices.
*   **Interactive & Dynamic Elements:** ✨ Engage with captivating carousels, smooth animations, and dynamic content loading for an enhanced and memorable user journey.
*   **Intuitive Navigation:** 🧭 A clear and intuitive menu structure provides effortless access to all sections of the website, guiding you to the information you need.
*   **Direct Contact & Enrollment:** 📝 Dedicated pages for inquiries and course enrollment make connecting with us and starting your journey straightforward.

## 🛠️ Technologies Used

This website is crafted using a powerful combination of standard web technologies and popular libraries, ensuring robustness and a rich user experience:

| Technology     | Description                                      |
| :------------- | :----------------------------------------------- |
| **HTML5**      | The backbone for structuring all content.        |
| **CSS3 / SCSS**| Styling with Bootstrap & custom themes.          |
| **JavaScript** | Powers all interactive functionalities and dynamic content. |
| **Bootstrap**  | A leading front-end framework for responsive design and pre-built, accessible components. |
| **Animate.css**| A collection of ready-to-use CSS animations to bring elements to life. |
| **Owl Carousel**| A touch-enabled jQuery plugin for creating beautiful, responsive content carousels. |
| **jQuery Easing**| Enhances animations with advanced easing functions for smoother transitions. |
| **Waypoints**  | A JavaScript library that triggers functions as you scroll to specific elements. |
| **Wow.js**     | Reveals CSS animations as you scroll down the page, adding a dynamic touch. |

## 🚀 Getting Started (Quick & Easy!)

As a static HTML website, getting started is incredibly simple – no complex server setup or build process is required!

1.  **Clone the Repository (if applicable):** If you obtained this project via a Git repository, clone it to your local machine:
    ```bash
    git clone <repository-url>
    ```
2.  **Open in Your Browser:** Navigate to the project directory and simply open any `.html` file (e.g., `index.html`, `about.html`, `courses.html`) directly in your preferred web browser. The website will load instantly!

## 📂 Project Structure

caddcenter/
├───*.html               # Main website pages (e.g., index.html, about.html, courses.html)
├───css/                 # Custom and compiled third-party CSS files
├───js/                  # Custom JavaScript files
├───lib/                 # Third-party JavaScript and CSS libraries (Animate, Easing, Owl Carousel, Waypoints, Wow)
├───Public/              # Images, videos, and other static assets 🖼️
├───scss/                # SCSS source files for Bootstrap customization
└───README.md            # This file 📄

## ⚖️ License

This project is licensed under the terms specified in the `LICENSE.txt` file.

## 📞 Contact

For any inquiries or support, please refer to the contact information provided on the website's dedicated contact page (`contact.html`). We look forward to hearing from you!